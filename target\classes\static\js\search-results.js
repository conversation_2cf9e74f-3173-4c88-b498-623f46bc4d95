/**
 * 搜索结果页面专用JavaScript功能
 * <AUTHOR>
 * @version 1.0.0
 */

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeSearchResultsPage();
});

/**
 * 初始化搜索结果页面
 */
function initializeSearchResultsPage() {
    initializeLazyLoading();
    initializeVideoCards();
    initializePagination();
    initializeSearchForm();
    highlightSearchKeyword();
    console.log('搜索结果页面初始化完成');
}

/**
 * 初始化懒加载
 */
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    img.classList.add('loaded');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(function(img) {
            imageObserver.observe(img);
        });
    } else {
        // 降级处理
        images.forEach(function(img) {
            img.src = img.dataset.src;
            img.classList.add('loaded');
        });
    }
}

/**
 * 初始化视频卡片
 */
function initializeVideoCards() {
    const videoCards = document.querySelectorAll('.video-card');
    
    videoCards.forEach(function(card) {
        // 添加悬停效果
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
        
        // 处理图片加载错误
        const img = card.querySelector('img');
        if (img) {
            img.addEventListener('error', function() {
                this.src = '/images/default-thumbnail.jpg';
                this.alt = '默认缩略图';
            });
        }
    });
}

/**
 * 初始化分页
 */
function initializePagination() {
    const paginationLinks = document.querySelectorAll('.pagination a');
    
    paginationLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            // 添加加载状态
            const spinner = document.createElement('i');
            spinner.className = 'fas fa-spinner fa-spin me-1';
            this.insertBefore(spinner, this.firstChild);
            this.style.pointerEvents = 'none';
        });
    });
}

/**
 * 初始化搜索表单
 */
function initializeSearchForm() {
    const searchForm = document.querySelector('form[action="/search"]');
    const searchInput = document.querySelector('input[name="keyword"]');
    
    if (searchForm && searchInput) {
        // 获取当前搜索关键词
        const urlParams = new URLSearchParams(window.location.search);
        const currentKeyword = urlParams.get('keyword');
        
        if (currentKeyword) {
            searchInput.value = currentKeyword;
        }
        
        // 表单提交处理
        searchForm.addEventListener('submit', function(e) {
            const keyword = searchInput.value.trim();
            if (!keyword) {
                e.preventDefault();
                showAlert('请输入搜索关键词', 'warning');
                searchInput.focus();
            }
        });
    }
}

/**
 * 高亮搜索关键词
 */
function highlightSearchKeyword() {
    const urlParams = new URLSearchParams(window.location.search);
    const keyword = urlParams.get('keyword');
    
    if (!keyword) return;
    
    const videoTitles = document.querySelectorAll('.video-card .card-title');
    const regex = new RegExp(`(${escapeRegExp(keyword)})`, 'gi');
    
    videoTitles.forEach(function(title) {
        const originalText = title.textContent;
        const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
        if (highlightedText !== originalText) {
            title.innerHTML = highlightedText;
        }
    });
}

/**
 * 转义正则表达式特殊字符
 * @param {string} string - 要转义的字符串
 * @returns {string} 转义后的字符串
 */
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * 执行新搜索
 * @param {string} keyword - 搜索关键词
 */
function performSearch(keyword) {
    if (!keyword || keyword.trim() === '') {
        showAlert('请输入搜索关键词', 'warning');
        return;
    }
    
    // 跳转到搜索结果页面
    window.location.href = `/search?keyword=${encodeURIComponent(keyword.trim())}`;
}

/**
 * 加载更多搜索结果
 * @param {string} keyword - 搜索关键词
 * @param {number} page - 页码
 */
async function loadMoreSearchResults(keyword, page) {
    try {
        const response = await fetch(`/api/videos/search?keyword=${encodeURIComponent(keyword)}&page=${page}&size=12`);
        const result = await response.json();
        
        if (result.success && result.data.length > 0) {
            appendVideosToGrid(result.data);
            updateLoadMoreButton(result.hasNext, keyword, page + 1);
        } else {
            showAlert('没有更多搜索结果了', 'info');
        }
        
    } catch (error) {
        console.error('加载更多搜索结果失败:', error);
        showAlert('加载失败，请稍后重试', 'danger');
    }
}

/**
 * 将视频添加到网格中
 * @param {Array} videos - 视频列表
 */
function appendVideosToGrid(videos) {
    const videoGrid = document.querySelector('.video-grid');
    if (!videoGrid) return;
    
    videos.forEach(function(video) {
        const videoCard = createVideoCard(video);
        videoGrid.appendChild(videoCard);
    });
    
    // 重新初始化新添加的卡片
    initializeVideoCards();
    
    // 重新高亮关键词
    highlightSearchKeyword();
}

/**
 * 创建视频卡片元素
 * @param {Object} video - 视频对象
 * @returns {HTMLElement} 视频卡片元素
 */
function createVideoCard(video) {
    const article = document.createElement('article');
    article.className = 'video-card card h-100 shadow-sm';
    
    article.innerHTML = `
        <a href="/play/${video.id}" class="video-thumbnail text-decoration-none">
            <img src="${video.thumbnailUrl || '/images/default-thumbnail.jpg'}"
                 class="card-img-top thumbnail-optimized"
                 alt="${video.title}"
                 loading="lazy"
                 style="height: 200px; object-fit: cover;">
        </a>
        <div class="card-body">
            <h3 class="card-title h5">${video.title}</h3>
            <div class="video-stats">
                <time class="video-date">${formatDate(video.createdTime)}</time>
                ${video.duration ? `<span class="video-duration">${formatDuration(video.duration)}</span>` : ''}
            </div>
        </div>
    `;
    
    return article;
}

/**
 * 更新加载更多按钮
 * @param {boolean} hasNext - 是否有下一页
 * @param {string} keyword - 搜索关键词
 * @param {number} nextPage - 下一页页码
 */
function updateLoadMoreButton(hasNext, keyword, nextPage) {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (!loadMoreBtn) return;
    
    if (hasNext) {
        loadMoreBtn.style.display = 'block';
        loadMoreBtn.onclick = () => loadMoreSearchResults(keyword, nextPage);
    } else {
        loadMoreBtn.style.display = 'none';
    }
}

/**
 * 格式化日期
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

/**
 * 格式化时长
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时长
 */
function formatDuration(seconds) {
    if (!seconds || seconds < 0) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

/**
 * 显示提示信息
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型
 */
function showAlert(message, type = 'info') {
    // 使用全局的showAlert函数，如果存在的话
    if (window.VideoPlayer && window.VideoPlayer.showAlert) {
        window.VideoPlayer.showAlert(message, type);
        return;
    }
    
    // 降级处理
    const alertContainer = document.createElement('div');
    alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertContainer.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    alertContainer.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertContainer);
    
    // 自动关闭
    setTimeout(function() {
        if (alertContainer.parentNode) {
            alertContainer.remove();
        }
    }, 3000);
}

/**
 * 清除搜索
 */
function clearSearch() {
    window.location.href = '/videos';
}

// 导出到全局作用域
window.SearchResultsPage = {
    performSearch,
    loadMoreSearchResults,
    clearSearch,
    highlightSearchKeyword,
    formatDate,
    formatDuration,
    showAlert
};
