<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    <meta name="msapplication-navbutton-color" content="#ffffff">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="supported-color-schemes" content="light">
    <title th:text="${pageTitle}">搜索结果 - 视频播放器</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/videos-style.css" rel="stylesheet">
    <link href="/css/search-results-style.css" rel="stylesheet">
</head>
<body style="background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important;">
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top" style="background-color: #0d6efd !important; color-scheme: light only !important;">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <ul class="navbar-nav d-flex flex-row mb-0">
                    <li class="nav-item">
                        <a class="nav-link px-2" href="/" style="color: rgba(255, 255, 255, 0.75) !important; color-scheme: light only !important;">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link px-2" href="/videos" style="color: rgba(255, 255, 255, 0.75) !important; color-scheme: light only !important;">
                            <i class="fas fa-video me-1"></i>视频
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link px-2" href="/admin" style="color: rgba(255, 255, 255, 0.75) !important; color-scheme: light only !important;">
                            <i class="fas fa-cog me-1"></i>管理
                        </a>
                    </li>
                </ul>

                <!-- 搜索框 -->
                <div class="search-container">
                    <form class="d-flex search-form" action="/search" method="get">
                        <input class="form-control search-input-mobile search-input-expanded" type="search" name="keyword"
                               th:value="${keyword}" placeholder="搜索..." style="background-color: rgba(255, 255, 255, 0.1) !important; border-color: rgba(255, 255, 255, 0.3) !important; color: #ffffff !important; color-scheme: light only !important;">
                        <button class="btn btn-outline-light btn-sm" type="button" onclick="toggleSearch()" style="border-color: rgba(255, 255, 255, 0.5) !important; color: rgba(255, 255, 255, 0.75) !important; color-scheme: light only !important;">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                    <a th:if="${keyword}" href="/videos" class="btn btn-outline-light btn-sm ms-1" title="清除搜索" style="border-color: rgba(255, 255, 255, 0.5) !important; color: rgba(255, 255, 255, 0.75) !important; color-scheme: light only !important;">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container">
        <!-- 搜索结果标题 -->
        <div class="row mt-4 mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-2">
                            <i class="fas fa-search text-primary me-2"></i>
                            搜索结果
                        </h1>
                        <p class="text-muted mb-0">
                            关键词: <span class="search-highlight" th:text="${keyword}">搜索关键词</span>
                            <span th:if="${totalItems > 0}">
                                - 找到 <strong th:text="${totalItems}">0</strong> 个结果
                            </span>
                        </p>
                    </div>
                    <!-- <a href="/videos" class="btn btn-outline-primary">
                        <i class="fas fa-list me-1"></i>查看全部视频
                    </a> -->
                </div>
            </div>
        </div>

        <!-- 搜索结果内容 -->
        <div class="row">
            <!-- 有搜索结果时显示 -->
            <div th:if="${videos != null and !videos.isEmpty()}">
                <div class="video-grid">
                    <article class="video-card card h-100 shadow-sm" th:each="video : ${videos}">
                        <a th:href="@{/play/{id}(id=${video.id})}" class="video-thumbnail text-decoration-none">
                            <img th:src="${video.thumbnailUrl}"
                                 class="card-img-top thumbnail-optimized"
                                 th:alt="${video.title}"
                                 loading="lazy"
                                 decoding="async"
                                 th:data-fallback-src="${video.thumbnailUrl}"
                                 onerror="this.src=this.getAttribute('data-fallback-src')"
                                 style="height: 200px; object-fit: cover;"
                                 onload="this.classList.add('loaded')"
                                 data-loaded="false">
                        </a>
                        <div class="card-body">
                            <h3 class="card-title h5" th:text="${video.title}">视频标题</h3>
                            <p class="card-text text-muted small" th:if="${video.description}" th:text="${video.description}">视频描述</p>
                            <div class="video-stats">
                                <time class="video-date" th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd')}">2024-01-01</time>
                            </div>
                        </div>
                    </article>
                </div>

                <!-- 分页导航 -->
                <nav th:if="${totalPages > 1}" aria-label="搜索结果分页" class="mt-5">
                    <ul class="pagination justify-content-center">
                        <!-- 上一页 -->
                        <li class="page-item" th:classappend="${currentPage == 0} ? 'disabled'">
                            <a class="page-link" th:href="@{/search(keyword=${keyword}, page=${currentPage - 1})}" 
                               th:if="${currentPage > 0}">
                                <i class="fas fa-chevron-left"></i> 上一页
                            </a>
                            <span class="page-link" th:if="${currentPage == 0}">
                                <i class="fas fa-chevron-left"></i> 上一页
                            </span>
                        </li>

                        <!-- 页码 -->
                        <li class="page-item" th:each="i : ${#numbers.sequence(0, totalPages - 1)}"
                            th:classappend="${i == currentPage} ? 'active'">
                            <a class="page-link" th:href="@{/search(keyword=${keyword}, page=${i})}" 
                               th:text="${i + 1}" th:if="${i != currentPage}">1</a>
                            <span class="page-link" th:if="${i == currentPage}" th:text="${i + 1}">1</span>
                        </li>

                        <!-- 下一页 -->
                        <li class="page-item" th:classappend="${currentPage >= totalPages - 1} ? 'disabled'">
                            <a class="page-link" th:href="@{/search(keyword=${keyword}, page=${currentPage + 1})}" 
                               th:if="${currentPage < totalPages - 1}">
                                下一页 <i class="fas fa-chevron-right"></i>
                            </a>
                            <span class="page-link" th:if="${currentPage >= totalPages - 1}">
                                下一页 <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- 无搜索结果时显示 -->
            <div th:if="${videos == null or videos.isEmpty()}" class="col-12">
                <div class="empty-state text-center py-5">
                    <i class="fas fa-search fa-4x text-muted mb-4"></i>
                    <h2 class="text-muted mb-4">未找到相关视频</h2>
                    <p class="text-muted mb-4">
                        没有找到包含 "<span class="search-highlight" th:text="${keyword}">搜索关键词</span>" 的视频
                    </p>
                    <div class="d-flex justify-content-center gap-3">
                        <a href="/videos" class="btn btn-primary">
                            <i class="fas fa-list me-2"></i>浏览所有视频
                        </a>
                        <button class="btn btn-outline-secondary" onclick="document.querySelector('input[name=keyword]').focus()">
                            <i class="fas fa-search me-2"></i>重新搜索
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="footer-single-container">
            <p class="spqk01">
                <span>轻康自然，享瘦生活。</span>
            </p>
            <p class="spqk02">
                <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">点这里：联系我们</a></small>
            </p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/search-results.js"></script>
</body>
</html>
