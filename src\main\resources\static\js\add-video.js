/**
 * 添加视频页面专用JavaScript功能
 * <AUTHOR>
 * @version 1.0.0
 */

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeAddVideoPage();
});

/**
 * 初始化添加视频页面
 */
function initializeAddVideoPage() {
    const form = document.getElementById('addVideoForm');
    if (form) {
        form.addEventListener('submit', handleVideoUpload);
    }
    console.log('添加视频页面初始化完成');
}

/**
 * 处理视频上传
 * @param {Event} event - 表单提交事件
 */
async function handleVideoUpload(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const progressContainer = document.getElementById('uploadProgress');
    const progressBar = progressContainer.querySelector('.progress-bar');
    
    try {
        // 验证表单
        if (!validateForm(form)) {
            return;
        }
        
        // 显示上传进度
        progressContainer.style.display = 'block';
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>上传中...';
        
        // 模拟进度更新
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
        }, 500);
        
        // 上传文件
        const response = await fetch('/api/upload/video-with-thumbnail', {
            method: 'POST',
            body: formData
        });
        
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('视频上传成功！🎉', 'success');
            setTimeout(() => {
                window.location.href = '/admin';
            }, 1500);
        } else {
            throw new Error(result.message || '上传失败');
        }
        
    } catch (error) {
        console.error('上传失败:', error);
        showAlert('上传失败: ' + error.message, 'danger');
        
        // 重置状态
        progressContainer.style.display = 'none';
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>保存视频';
    }
}

/**
 * 表单验证
 * @param {HTMLFormElement} form - 表单元素
 * @returns {boolean} 验证结果
 */
function validateForm(form) {
    const videoFile = form.querySelector('#videoFile');
    const title = form.querySelector('#title');
    
    if (!videoFile.files.length) {
        showAlert('请选择视频文件', 'warning');
        return false;
    }
    
    if (!title.value.trim()) {
        showAlert('请输入视频标题', 'warning');
        return false;
    }
    
    // 验证文件类型
    const file = videoFile.files[0];
    const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'];
    if (!allowedTypes.includes(file.type)) {
        showAlert('请选择支持的视频格式（MP4、AVI、MOV、WMV、FLV、WebM）', 'warning');
        return false;
    }
    
    // 验证文件大小（100MB限制）
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
        showAlert('视频文件大小不能超过100MB', 'warning');
        return false;
    }
    
    return true;
}

/**
 * 显示提示信息
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHtml);
    
    // 自动移除提示
    setTimeout(() => {
        const alertElement = document.getElementById(alertId);
        if (alertElement) {
            alertElement.remove();
        }
    }, 5000);
}

/**
 * 预览选中的视频文件
 * @param {HTMLInputElement} input - 文件输入框
 */
function previewVideo(input) {
    const file = input.files[0];
    if (!file) return;
    
    const previewContainer = document.getElementById('videoPreview');
    if (!previewContainer) return;
    
    // 创建视频预览元素
    const video = document.createElement('video');
    video.src = URL.createObjectURL(file);
    video.controls = true;
    video.style.width = '100%';
    video.style.maxHeight = '300px';
    video.className = 'mt-3 rounded';
    
    // 清空之前的预览
    previewContainer.innerHTML = '';
    previewContainer.appendChild(video);
    
    // 显示文件信息
    const fileInfo = document.createElement('div');
    fileInfo.className = 'mt-2 text-muted small';
    fileInfo.innerHTML = `
        <i class="fas fa-info-circle me-1"></i>
        文件名: ${file.name} | 
        大小: ${formatFileSize(file.size)} | 
        类型: ${file.type}
    `;
    previewContainer.appendChild(fileInfo);
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 重置表单
 */
function resetForm() {
    const form = document.getElementById('addVideoForm');
    if (form) {
        form.reset();
        
        // 清空预览
        const previewContainer = document.getElementById('videoPreview');
        if (previewContainer) {
            previewContainer.innerHTML = '';
        }
        
        // 隐藏进度条
        const progressContainer = document.getElementById('uploadProgress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
    }
}

// 导出到全局作用域
window.AddVideoPage = {
    handleVideoUpload,
    validateForm,
    showAlert,
    previewVideo,
    resetForm,
    formatFileSize
};
